/**
 * 简化版Token管理器 - 无感换号核心模块
 * 基于success case的核心逻辑，简化实现
 */

class SimpleTokenManager {
    // ========== 配置区域 - 方便修改 ==========
    static API_BASE_URL = 'https://augment-auto.techexpresser.com';  // 您的后端地址
    static AUTH_PASSWORD = 'your_secret_password_here_change_this';   // 您的认证密码
    static DEFAULT_TENANT_URL = 'https://d5.api.augmentcode.com/';
    static SCOPES = ['augment_external_v1_2024'];    // 🔑 与success case 1一致
    // =====================================

    /**
     * 获取设置（从globalState读取，影响官方插件行为）
     */
    async getSettings() {
        try {
            const apiBaseUrl = await this.context.globalState.get('apiBaseUrl') || SimpleTokenManager.API_BASE_URL;
            const defaultTenantUrl = await this.context.globalState.get('defaultTenantUrl') || SimpleTokenManager.DEFAULT_TENANT_URL;

            return {
                success: true,
                settings: {
                    apiBaseUrl,
                    defaultTenantUrl
                }
            };
        } catch (error) {
            this.logger.error('获取设置失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 保存设置（保存到globalState，影响官方插件行为）
     */
    async saveSettings(settings) {
        try {
            if (settings.apiBaseUrl) {
                await this.context.globalState.update('apiBaseUrl', settings.apiBaseUrl);
            }
            if (settings.defaultTenantUrl) {
                await this.context.globalState.update('defaultTenantUrl', settings.defaultTenantUrl);
            }

            this.logger.info('设置保存成功');
            return {
                success: true,
                message: '设置保存成功！'
            };
        } catch (error) {
            this.logger.error('保存设置失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 重置设置为默认值
     */
    async resetSettings() {
        try {
            await this.context.globalState.update('apiBaseUrl', undefined);
            await this.context.globalState.update('defaultTenantUrl', undefined);

            this.logger.info('设置已重置为默认值');
            return {
                success: true,
                message: '设置已重置为默认值！'
            };
        } catch (error) {
            this.logger.error('重置设置失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 测试API连接（影响官方插件的网络配置）
     */
    async testApiConnection() {
        try {
            const settingsResult = await this.getSettings();
            const apiBaseUrl = settingsResult.success ? settingsResult.settings.apiBaseUrl : SimpleTokenManager.API_BASE_URL;

            const response = await fetch(`${apiBaseUrl}/api/tokens`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${SimpleTokenManager.AUTH_PASSWORD}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
                },
                timeout: 5000
            });

            if (response.ok) {
                return {
                    success: true,
                    message: 'API连接成功！'
                };
            } else {
                return {
                    success: false,
                    message: `API连接失败: HTTP ${response.status}`
                };
            }
        } catch (error) {
            this.logger.error('测试API连接失败', error);
            return {
                success: false,
                message: `API连接失败: ${error.message}`
            };
        }
    }

    constructor() {
        this.context = null;
        this.logger = {
            info: (msg) => console.log(`[TokenManager] ${msg}`),
            error: (msg, err) => console.error(`[TokenManager] ${msg}`, err),
            warn: (msg) => console.warn(`[TokenManager] ${msg}`)
        };
    }

    async initialize(context) {
        this.context = context;
        this.logger.info('Token Manager initialized');
    }

    /**
     * 测试API连接
     */
    async testApiConnection() {
        try {
            const settingsResult = await this.getSettings();
            const apiBaseUrl = settingsResult.success ? settingsResult.settings.apiBaseUrl : SimpleTokenManager.API_BASE_URL;

            this.logger.info(`测试API连接: ${apiBaseUrl}`);

            // 先测试健康检查端点
            try {
                const healthResponse = await fetch(`${apiBaseUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
                    },
                    timeout: 5000
                });
                this.logger.info(`健康检查响应: ${healthResponse.status}`);
            } catch (healthError) {
                this.logger.warn(`健康检查失败: ${healthError.message}`);
            }

            // 测试认证端点
            const response = await fetch(`${apiBaseUrl}/api/tokens`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${SimpleTokenManager.AUTH_PASSWORD}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
                },
                timeout: 10000
            });

            return {
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                url: `${apiBaseUrl}/api/tokens`
            };

        } catch (error) {
            this.logger.error('API连接测试失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取可用的token列表
     */
    async getAvailableTokens() {
        try {
            // 使用动态设置，影响官方插件行为
            const settingsResult = await this.getSettings();
            const apiBaseUrl = settingsResult.success ? settingsResult.settings.apiBaseUrl : SimpleTokenManager.API_BASE_URL;

            // 🔍 调试信息
            this.logger.info(`正在调用API: ${apiBaseUrl}/api/tokens`);
            this.logger.info(`使用认证密码: ${SimpleTokenManager.AUTH_PASSWORD.substring(0, 10)}...`);

            const requestHeaders = {
                'Authorization': `Bearer ${SimpleTokenManager.AUTH_PASSWORD}`,
                'Content-Type': 'application/json',
                'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
            };

            this.logger.info(`请求头: ${JSON.stringify(requestHeaders, null, 2)}`);

            const response = await fetch(`${apiBaseUrl}/api/tokens`, {
                method: 'GET',
                headers: requestHeaders,
                timeout: 10000
            });

            this.logger.info(`响应状态: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                // 尝试读取错误响应体
                let errorBody = '';
                try {
                    errorBody = await response.text();
                    this.logger.error(`错误响应体: ${errorBody}`);
                } catch (e) {
                    this.logger.error('无法读取错误响应体');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}. 响应: ${errorBody}`);
            }

            const data = await response.json();

            // 根据您的API文档，返回格式是单个token对象，不是数组
            if (data.success && data.token) {
                this.logger.info(`获取到可用token: ${data.token.id || 'unknown'}`);
                return {
                    success: true,
                    tokens: [data.token] // 将单个token包装成数组以保持兼容性
                };
            } else {
                this.logger.warn('API返回无可用token');
                return {
                    success: false,
                    error: data.message || 'No available tokens',
                    tokens: []
                };
            }
        } catch (error) {
            this.logger.error('获取token列表失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 注入token到VSCode secrets - 核心功能
     * 采用增量更新策略，保留现有session中的重要字段
     */
    async injectToken(accessToken, tenantURL = null) {
        try {
            // 使用动态设置，影响官方插件行为
            const settingsResult = await this.getSettings();
            const defaultTenantUrl = settingsResult.success ? settingsResult.settings.defaultTenantUrl : SimpleTokenManager.DEFAULT_TENANT_URL;

            // 🔑 关键修复：先获取现有的sessions数据，保留重要字段
            const currentValue = await this.context.secrets.get('augment.sessions');
            let sessionData = {};

            if (currentValue) {
                try {
                    sessionData = JSON.parse(currentValue);
                    this.logger.info('保留现有session数据，进行增量更新');
                } catch (error) {
                    this.logger.warn('解析现有session数据失败，创建新对象', error);
                    sessionData = {};
                }
            } else {
                this.logger.info('未找到现有session数据，创建新对象');
            }

            // 🎯 只更新必要的字段，保留其他重要信息
            sessionData.accessToken = accessToken;
            sessionData.tenantURL = tenantURL || defaultTenantUrl;

            // 只在字段不存在时才设置默认值
            if (!sessionData.scopes) {
                sessionData.scopes = SimpleTokenManager.SCOPES;
            }

            // 关键：使用相同的键名劫持原插件的存储
            await this.context.secrets.store('augment.sessions', JSON.stringify(sessionData));

            this.logger.info('Token注入成功（增量更新）');
            return {
                success: true,
                data: sessionData
            };
        } catch (error) {
            this.logger.error('Token注入失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取当前存储的token信息
     */
    async getCurrentToken() {
        try {
            const sessionData = await this.context.secrets.get('augment.sessions');
            if (sessionData) {
                const data = JSON.parse(sessionData);
                return {
                    success: true,
                    accessToken: data.accessToken,
                    tenantURL: data.tenantURL,
                    data: data
                };
            } else {
                return {
                    success: false,
                    error: '未找到token信息'
                };
            }
        } catch (error) {
            this.logger.error('获取当前token失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 复制accessToken到剪贴板（影响官方插件的认证）
     */
    async copyAccessToken() {
        try {
            const currentResult = await this.getCurrentToken();
            if (!currentResult.success) {
                return currentResult;
            }

            const accessToken = currentResult.data.accessToken;
            await vscode.env.clipboard.writeText(accessToken);

            return {
                success: true,
                message: 'AccessToken已复制到剪贴板'
            };
        } catch (error) {
            this.logger.error('复制AccessToken失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 手动设置accessToken（影响官方插件认证）
     */
    async setAccessToken(accessToken) {
        try {
            if (!accessToken || accessToken.trim().length === 0) {
                return {
                    success: false,
                    error: 'AccessToken不能为空'
                };
            }

            // 获取当前会话数据，保留tenantURL和scopes
            const currentResult = await this.getCurrentToken();
            let tenantURL = SimpleTokenManager.DEFAULT_TENANT_URL;
            let scopes = SimpleTokenManager.SCOPES;

            if (currentResult.success) {
                tenantURL = currentResult.data.tenantURL || tenantURL;
                scopes = currentResult.data.scopes || scopes;
            }

            // 注入新的accessToken
            return await this.injectToken(accessToken.trim(), tenantURL);
        } catch (error) {
            this.logger.error('设置AccessToken失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取或生成设备码
     */
    async getDeviceId() {
        try {
            let deviceId = await this.context.globalState.get('sessionId');
            if (!deviceId) {
                deviceId = require('crypto').randomUUID();
                await this.context.globalState.update('sessionId', deviceId);
                this.logger.info(`生成新设备码: ${deviceId}`);
            }
            return {
                success: true,
                deviceId: deviceId
            };
        } catch (error) {
            this.logger.error('获取设备码失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 更新设备码
     */
    async updateDeviceId() {
        try {
            const newDeviceId = require('crypto').randomUUID();
            await this.context.globalState.update('sessionId', newDeviceId);
            this.logger.info(`设备码已更新: ${newDeviceId}`);
            return {
                success: true,
                deviceId: newDeviceId,
                message: `设备码已更新: ${newDeviceId}`
            };
        } catch (error) {
            this.logger.error('更新设备码失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 一键更新token - 自动选择第一个可用token
     */
    async quickUpdateToken() {
        try {
            const tokensResult = await this.getAvailableTokens();
            if (!tokensResult.success || !tokensResult.tokens.length) {
                return {
                    success: false,
                    error: '没有可用的token'
                };
            }

            // 选择第一个token
            const selectedToken = tokensResult.tokens[0];
            const injectResult = await this.injectToken(
                selectedToken.accessToken,
                selectedToken.tenantURL
            );

            if (injectResult.success) {
                this.logger.info(`一键更新成功，使用token: ${selectedToken.id || 'unknown'}`);
                return {
                    success: true,
                    message: `一键更新成功！使用了来自 ${SimpleTokenManager.API_BASE_URL} 的token`,
                    data: injectResult.data
                };
            } else {
                return injectResult;
            }
        } catch (error) {
            this.logger.error('一键更新失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    dispose() {
        this.logger.info('Token Manager disposed');
    }
}

module.exports = SimpleTokenManager;
